<script setup>
import { ref, reactive } from 'vue'
import { RouterLink } from 'vue-router'

// 登录状态与KYC状态
const isLoggedIn = ref(true)
const isKYCVerified = ref(true)

// 步骤状态
const currentStep = ref(1)
const totalSteps = 5

// 选择的基金
const selectedFund = ref(null)

// 可选择的基金列表
const fundsList = ref([
  {
    id: 'WL-2024-ESG-001',
    title: '剧投投文旅ESG影视基金',
    type: '股权型',
    risk: 'R3',
    minInvestment: 3000000,
    expectedReturn: '15-20%',
  },
  {
    id: 'WL-2024-IP-002',
    title: '剧投投IP孵化基金',
    type: '混合型',
    risk: 'R4',
    minInvestment: 1000000,
    expectedReturn: '20-25%',
  },
  {
    id: 'WL-2024-DEBT-003',
    title: '剧投投文旅债权基金A',
    type: '债权型',
    risk: 'R2',
    minInvestment: 5000000,
    expectedReturn: '8-10%',
  },
  {
    id: 'WL-2024-DRAMA-005',
    title: '剧投投精品短剧基金',
    type: '混合型',
    risk: 'R3',
    minInvestment: 1000000,
    expectedReturn: '15-25%',
  },
])

// 认购表单数据
const applyForm = reactive({
  fundId: '',
  investAmount: '',
  name: '',
  idType: '身份证',
  idNumber: '',
  mobile: '',
  email: '',
  address: '',
  bankName: '',
  bankAccount: '',
  bankBranch: '',
  investorType: '个人',
  investorExperience: '',
  agreeTerms: false,
  additionalInfo: ''
})

// 风险评估答案
const riskAssessment = reactive({
  investExperience: '',
  riskTolerance: '',
  investHorizon: '',
  assetAllocation: '',
  incomeStability: '',
})

// 风险等级对应的颜色
const riskLevelColors = {
  'R1': 'bg-green-500',
  'R2': 'bg-blue-500',
  'R3': 'bg-yellow-500',
  'R4': 'bg-orange-500',
  'R5': 'bg-red-500',
}

// 下一步
const nextStep = () => {
  if (currentStep.value < totalSteps) {
    currentStep.value++
  }
}

// 上一步
const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--
  }
}

// 选择基金
const selectFund = (fund) => {
  selectedFund.value = fund
  applyForm.fundId = fund.id
  nextStep()
}

// 格式化金额
const formatCurrency = (amount) => {
  if (amount >= ********) {
    return `${(amount / ********).toFixed(2)}亿`
  } else if (amount >= 10000) {
    return `${(amount / 10000).toFixed(0)}万`
  } else {
    return `${amount.toFixed(0)}`
  }
}

// 提交表单
const submitApplication = () => {
  // 实际项目中这里会调用API进行表单提交
  console.log('提交认购申请', applyForm)
  nextStep()
}

// 发送验证码
const sendVerificationCode = () => {
  // 模拟发送验证码
  alert('验证码已发送到您的手机')
}
</script>

<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="container mx-auto px-4">
      <!-- 页面标题 -->
      <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
          <h1 class="text-2xl md:text-3xl font-bold">基金认购</h1>
          <p class="text-gray-600 mt-2">在线申请投资旅文基金</p>
        </div>
        <div class="mt-4 md:mt-0">
          <RouterLink to="/funds" class="text-primary flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 17l-5-5m0 0l5-5m-5 5h12" />
            </svg>
            返回基金主页
          </RouterLink>
        </div>
      </div>
      
      <!-- 未登录或未KYC认证提示 -->
      <div v-if="!isLoggedIn || !isKYCVerified" class="bg-white rounded-xl shadow-md p-8 text-center">
        <svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>
        <h2 class="text-2xl font-bold mb-4">
          {{ !isLoggedIn ? '需要登录' : '需要完成投资者认证' }}
        </h2>
        <p class="text-gray-600 mb-8 max-w-md mx-auto">
          {{ !isLoggedIn 
            ? '请先登录您的账户，再进行基金认购' 
            : '根据监管要求，投资者需完成实名认证和合格投资者认证后方可认购基金产品' 
          }}
        </p>
        
        <RouterLink 
          :to="!isLoggedIn ? '/dashboard' : '/dashboard/kyc'"
          class="inline-block px-6 py-3 bg-primary hover:bg-primary-dark text-white font-medium rounded-md transition-colors"
        >
          {{ !isLoggedIn ? '立即登录' : '完成投资者认证' }}
        </RouterLink>
      </div>
      
      <!-- 已认证状态：认购流程 -->
      <div v-else>
        <!-- 步骤指示器 -->
        <div class="bg-white rounded-xl shadow-md p-6 mb-8">
          <div class="flex justify-between items-start relative">
            <!-- 步骤连接线 - 对齐到圆圈中心 -->
            <div class="absolute top-5 left-0 w-full h-1 bg-gray-200 -z-10"></div>

            <!-- 步骤节点 -->
            <div v-for="step in totalSteps" :key="step" class="flex flex-col items-center z-10">
              <div
                class="w-10 h-10 rounded-full flex items-center justify-center font-bold mb-2 border-2"
                :class="step < currentStep
                  ? 'bg-primary text-white border-primary'
                  : step === currentStep
                    ? 'bg-white text-primary border-primary'
                    : 'bg-white text-gray-400 border-gray-300'"
              >
                {{ step }}
              </div>
              <div class="text-xs text-center w-20">
                {{ step === 1 ? '选择基金' :
                   step === 2 ? '风险评估' :
                   step === 3 ? '填写资料' :
                   step === 4 ? '确认信息' :
                   '完成' }}
              </div>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-md overflow-hidden">
          <!-- 步骤1：选择基金 -->
          <div v-if="currentStep === 1" class="p-6">
            <h2 class="text-xl font-bold mb-6">选择您要认购的基金</h2>
            
            <div class="space-y-4">
              <div 
                v-for="fund in fundsList" 
                :key="fund.id"
                class="border rounded-lg p-4 hover:shadow-md transition-shadow duration-300 cursor-pointer"
                :class="{'border-primary': selectedFund?.id === fund.id}"
                @click="selectFund(fund)"
              >
                <div class="flex justify-between items-start">
                  <div>
                    <h3 class="font-bold text-lg">{{ fund.title }}</h3>
                    <div class="text-sm text-gray-500">{{ fund.id }}</div>
                  </div>
                  <div class="flex items-center space-x-2">
                    <span class="bg-gray-100 text-gray-700 px-2 py-1 rounded-md text-sm">
                      {{ fund.type }}
                    </span>
                    <span :class="[riskLevelColors[fund.risk], 'text-white px-2 py-1 rounded-md text-sm']">
                      {{ fund.risk }}
                    </span>
                  </div>
                </div>
                
                <div class="mt-4 grid grid-cols-2 gap-4">
                  <div>
                    <div class="text-sm text-gray-600">最低认购金额</div>
                    <div class="font-medium">{{ formatCurrency(fund.minInvestment) }}元</div>
                  </div>
                  <div>
                    <div class="text-sm text-gray-600">预期年化收益</div>
                    <div class="font-medium text-primary">{{ fund.expectedReturn }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 步骤2：风险评估 -->
          <div v-else-if="currentStep === 2" class="p-6">
            <h2 class="text-xl font-bold mb-2">风险承受能力评估</h2>
            <p class="text-gray-600 mb-6">请根据您的实际情况回答以下问题，我们将评估您的风险承受能力</p>
            
            <form @submit.prevent="nextStep" class="space-y-6">
              <!-- 投资经验 -->
              <div>
                <label class="block text-gray-700 font-medium mb-2">您的投资经验有多久？</label>
                <div class="space-y-2">
                  <label class="flex items-center">
                    <input type="radio" v-model="riskAssessment.investExperience" value="1" required class="mr-2 accent-primary">
                    <span>无投资经验</span>
                  </label>
                  <label class="flex items-center">
                    <input type="radio" v-model="riskAssessment.investExperience" value="2" class="mr-2 accent-primary">
                    <span>1年以下</span>
                  </label>
                  <label class="flex items-center">
                    <input type="radio" v-model="riskAssessment.investExperience" value="3" class="mr-2 accent-primary">
                    <span>1-3年</span>
                  </label>
                  <label class="flex items-center">
                    <input type="radio" v-model="riskAssessment.investExperience" value="4" class="mr-2 accent-primary">
                    <span>3-5年</span>
                  </label>
                  <label class="flex items-center">
                    <input type="radio" v-model="riskAssessment.investExperience" value="5" class="mr-2 accent-primary">
                    <span>5年以上</span>
                  </label>
                </div>
              </div>
              
              <!-- 风险偏好 -->
              <div>
                <label class="block text-gray-700 font-medium mb-2">您愿意承担多大的投资风险？</label>
                <div class="space-y-2">
                  <label class="flex items-center">
                    <input type="radio" v-model="riskAssessment.riskTolerance" value="1" required class="mr-2 accent-primary">
                    <span>非常保守，本金安全最重要</span>
                  </label>
                  <label class="flex items-center">
                    <input type="radio" v-model="riskAssessment.riskTolerance" value="2" class="mr-2 accent-primary">
                    <span>保守，接受小幅波动</span>
                  </label>
                  <label class="flex items-center">
                    <input type="radio" v-model="riskAssessment.riskTolerance" value="3" class="mr-2 accent-primary">
                    <span>适中，能承受一定波动</span>
                  </label>
                  <label class="flex items-center">
                    <input type="radio" v-model="riskAssessment.riskTolerance" value="4" class="mr-2 accent-primary">
                    <span>进取，为更高收益愿意承担较大风险</span>
                  </label>
                  <label class="flex items-center">
                    <input type="radio" v-model="riskAssessment.riskTolerance" value="5" class="mr-2 accent-primary">
                    <span>激进，追求高收益，能接受大幅波动</span>
                  </label>
                </div>
              </div>
              
              <!-- 投资期限 -->
              <div>
                <label class="block text-gray-700 font-medium mb-2">您的投资期限是多久？</label>
                <div class="space-y-2">
                  <label class="flex items-center">
                    <input type="radio" v-model="riskAssessment.investHorizon" value="1" required class="mr-2 accent-primary">
                    <span>3个月以内</span>
                  </label>
                  <label class="flex items-center">
                    <input type="radio" v-model="riskAssessment.investHorizon" value="2" class="mr-2 accent-primary">
                    <span>3-12个月</span>
                  </label>
                  <label class="flex items-center">
                    <input type="radio" v-model="riskAssessment.investHorizon" value="3" class="mr-2 accent-primary">
                    <span>1-3年</span>
                  </label>
                  <label class="flex items-center">
                    <input type="radio" v-model="riskAssessment.investHorizon" value="4" class="mr-2 accent-primary">
                    <span>3-5年</span>
                  </label>
                  <label class="flex items-center">
                    <input type="radio" v-model="riskAssessment.investHorizon" value="5" class="mr-2 accent-primary">
                    <span>5年以上</span>
                  </label>
                </div>
              </div>
              
              <div class="flex justify-between pt-4">
                <button 
                  type="button"
                  @click="prevStep"
                  class="px-6 py-3 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                >
                  上一步
                </button>
                <button 
                  type="submit"
                  class="px-6 py-3 bg-primary hover:bg-primary-dark text-white rounded-md transition-colors"
                >
                  下一步
                </button>
              </div>
            </form>
          </div>
          
          <!-- 步骤3：填写认购资料 -->
          <div v-else-if="currentStep === 3" class="p-6">
            <h2 class="text-xl font-bold mb-2">填写认购资料</h2>
            <p class="text-gray-600 mb-6">请填写您的个人信息和认购金额</p>
            
            <form @submit.prevent="nextStep" class="space-y-6">
              <!-- 基本信息 -->
              <div class="bg-gray-50 p-4 rounded-lg mb-4">
                <h3 class="text-lg font-medium mb-3">基金信息</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <div class="text-sm text-gray-600">基金名称</div>
                    <div class="font-medium">{{ selectedFund.title }}</div>
                  </div>
                  <div>
                    <div class="text-sm text-gray-600">基金代码</div>
                    <div class="font-medium">{{ selectedFund.id }}</div>
                  </div>
                  <div>
                    <div class="text-sm text-gray-600">最低认购金额</div>
                    <div class="font-medium">{{ formatCurrency(selectedFund.minInvestment) }}元</div>
                  </div>
                  <div>
                    <div class="text-sm text-gray-600">预期收益</div>
                    <div class="font-medium text-primary">{{ selectedFund.expectedReturn }}</div>
                  </div>
                </div>
              </div>
              
              <!-- 认购金额 -->
              <div>
                <label for="investAmount" class="block text-gray-700 font-medium mb-2">认购金额（元）</label>
                <input 
                  type="number" 
                  id="investAmount" 
                  v-model="applyForm.investAmount" 
                  :min="selectedFund.minInvestment"
                  :step="100000"
                  required
                  class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                  placeholder="请输入认购金额"
                >
                <p class="text-sm text-gray-500 mt-1">最低认购金额：{{ formatCurrency(selectedFund.minInvestment) }}元</p>
              </div>
              
              <hr class="my-6">
              
              <!-- 个人信息 -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label for="name" class="block text-gray-700 font-medium mb-2">姓名</label>
                  <input 
                    type="text" 
                    id="name" 
                    v-model="applyForm.name" 
                    required
                    class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                    placeholder="请输入您的姓名"
                  >
                </div>
                
                <div>
                  <label for="idType" class="block text-gray-700 font-medium mb-2">证件类型</label>
                  <select 
                    id="idType" 
                    v-model="applyForm.idType" 
                    required
                    class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                  >
                    <option>身份证</option>
                    <option>护照</option>
                    <option>其他</option>
                  </select>
                </div>
                
                <div>
                  <label for="idNumber" class="block text-gray-700 font-medium mb-2">证件号码</label>
                  <input 
                    type="text" 
                    id="idNumber" 
                    v-model="applyForm.idNumber" 
                    required
                    class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                    placeholder="请输入证件号码"
                  >
                </div>
                
                <div>
                  <label for="mobile" class="block text-gray-700 font-medium mb-2">手机号码</label>
                  <div class="flex">
                    <input 
                      type="tel" 
                      id="mobile" 
                      v-model="applyForm.mobile" 
                      required
                      class="flex-grow px-4 py-3 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                      placeholder="请输入手机号码"
                    >
                    <button 
                      type="button"
                      @click="sendVerificationCode"
                      class="px-4 bg-gray-100 border border-l-0 border-gray-300 text-gray-700 rounded-r-md hover:bg-gray-200 transition-colors whitespace-nowrap"
                    >
                      获取验证码
                    </button>
                  </div>
                </div>
                
                <div>
                  <label for="email" class="block text-gray-700 font-medium mb-2">电子邮箱</label>
                  <input 
                    type="email" 
                    id="email" 
                    v-model="applyForm.email" 
                    required
                    class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                    placeholder="请输入电子邮箱"
                  >
                </div>
                
                <div class="md:col-span-2">
                  <label for="address" class="block text-gray-700 font-medium mb-2">联系地址</label>
                  <input 
                    type="text" 
                    id="address" 
                    v-model="applyForm.address" 
                    required
                    class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                    placeholder="请输入联系地址"
                  >
                </div>
              </div>
              
              <hr class="my-6">
              
              <!-- 银行账户信息 -->
              <h3 class="text-lg font-medium">银行账户信息</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label for="bankName" class="block text-gray-700 font-medium mb-2">开户银行</label>
                  <input 
                    type="text" 
                    id="bankName" 
                    v-model="applyForm.bankName" 
                    required
                    class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                    placeholder="请输入开户银行"
                  >
                </div>
                
                <div>
                  <label for="bankAccount" class="block text-gray-700 font-medium mb-2">银行账号</label>
                  <input 
                    type="text" 
                    id="bankAccount" 
                    v-model="applyForm.bankAccount" 
                    required
                    class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                    placeholder="请输入银行账号"
                  >
                </div>
                
                <div>
                  <label for="bankBranch" class="block text-gray-700 font-medium mb-2">开户支行</label>
                  <input 
                    type="text" 
                    id="bankBranch" 
                    v-model="applyForm.bankBranch" 
                    required
                    class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                    placeholder="请输入开户支行"
                  >
                </div>
              </div>
              
              <!-- 协议勾选 -->
              <div class="mt-8">
                <label class="flex items-start">
                  <input 
                    type="checkbox" 
                    v-model="applyForm.agreeTerms" 
                    required
                    class="mt-1 mr-3 accent-primary"
                  >
                  <span class="text-sm text-gray-700">
                    我已阅读并同意《基金认购协议》《风险揭示书》《隐私政策》，知晓投资风险，承诺所填信息真实有效。
                  </span>
                </label>
              </div>
              
              <div class="flex justify-between pt-4">
                <button 
                  type="button"
                  @click="prevStep"
                  class="px-6 py-3 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                >
                  上一步
                </button>
                <button 
                  type="submit"
                  class="px-6 py-3 bg-primary hover:bg-primary-dark text-white rounded-md transition-colors"
                >
                  下一步
                </button>
              </div>
            </form>
          </div>
          
          <!-- 步骤4：确认信息 -->
          <div v-else-if="currentStep === 4" class="p-6">
            <h2 class="text-xl font-bold mb-6">确认认购信息</h2>
            
            <div class="bg-gray-50 p-6 rounded-lg mb-8">
              <h3 class="text-lg font-medium mb-4">认购详情</h3>
              
              <div class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <div class="text-sm text-gray-600">基金名称</div>
                    <div class="font-medium">{{ selectedFund.title }}</div>
                  </div>
                  <div>
                    <div class="text-sm text-gray-600">基金代码</div>
                    <div class="font-medium">{{ selectedFund.id }}</div>
                  </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <div class="text-sm text-gray-600">认购金额</div>
                    <div class="text-xl font-bold text-primary">
                      {{ formatCurrency(parseInt(applyForm.investAmount || 0)) }}元
                    </div>
                  </div>
                  <div>
                    <div class="text-sm text-gray-600">预期年化收益</div>
                    <div class="font-medium">{{ selectedFund.expectedReturn }}</div>
                  </div>
                </div>
                
                <div class="border-t pt-4 mt-4">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <div class="text-sm text-gray-600">认购人姓名</div>
                      <div class="font-medium">{{ applyForm.name }}</div>
                    </div>
                    <div>
                      <div class="text-sm text-gray-600">证件号码</div>
                      <div class="font-medium">{{ applyForm.idNumber }}</div>
                    </div>
                  </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <div class="text-sm text-gray-600">手机号码</div>
                    <div class="font-medium">{{ applyForm.mobile }}</div>
                  </div>
                  <div>
                    <div class="text-sm text-gray-600">电子邮箱</div>
                    <div class="font-medium">{{ applyForm.email }}</div>
                  </div>
                </div>
                
                <div class="border-t pt-4 mt-4">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <div class="text-sm text-gray-600">开户银行</div>
                      <div class="font-medium">{{ applyForm.bankName }}</div>
                    </div>
                    <div>
                      <div class="text-sm text-gray-600">银行账号</div>
                      <div class="font-medium">{{ applyForm.bankAccount }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 风险提示 -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-8">
              <div class="flex">
                <svg class="w-5 h-5 text-yellow-500 mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <div>
                  <h4 class="font-medium text-yellow-800 mb-1">风险提示</h4>
                  <p class="text-sm text-yellow-700">
                    基金有风险，投资需谨慎。本产品不保证本金安全，也不保证一定盈利或最低收益。过往业绩不代表未来表现。请务必仔细阅读基金合同、风险揭示书等相关文件，并根据自身风险承受能力审慎投资。
                  </p>
                </div>
              </div>
            </div>
            
            <div class="flex justify-between">
              <button 
                type="button"
                @click="prevStep"
                class="px-6 py-3 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
              >
                上一步
              </button>
              <button 
                type="button"
                @click="submitApplication"
                class="px-6 py-3 bg-primary hover:bg-primary-dark text-white rounded-md transition-colors"
              >
                确认提交
              </button>
            </div>
          </div>
          
          <!-- 步骤5：完成 -->
          <div v-else-if="currentStep === 5" class="p-8 text-center">
            <svg class="w-16 h-16 mx-auto text-green-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h2 class="text-2xl font-bold mb-4">认购申请已提交成功！</h2>
            <p class="text-gray-600 mb-8 max-w-md mx-auto">
              您的基金认购申请已成功提交，我们的客服人员将在1-2个工作日内与您联系确认。
            </p>
            
            <div class="bg-gray-50 p-6 rounded-lg mb-8 max-w-md mx-auto">
              <div class="flex justify-between mb-3">
                <span class="text-gray-600">申请编号</span>
                <span class="font-medium">{{new Date().getTime()}}</span>
              </div>
              <div class="flex justify-between mb-3">
                <span class="text-gray-600">基金产品</span>
                <span class="font-medium">{{ selectedFund.title }}</span>
              </div>
              <div class="flex justify-between mb-3">
                <span class="text-gray-600">认购金额</span>
                <span class="font-medium text-primary">{{ formatCurrency(parseInt(applyForm.investAmount || 0)) }}元</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">申请时间</span>
                <span class="font-medium">{{new Date().toLocaleString('zh-CN')}}</span>
              </div>
            </div>
            
            <div class="flex justify-center space-x-4">
              <RouterLink 
                to="/dashboard" 
                class="px-6 py-3 border border-primary text-primary rounded-md hover:bg-primary hover:text-white transition-colors"
              >
                进入投资者中心
              </RouterLink>
              <RouterLink 
                to="/funds" 
                class="px-6 py-3 bg-primary hover:bg-primary-dark text-white rounded-md transition-colors"
              >
                返回基金主页
              </RouterLink>
            </div>
          </div>
        </div>
        
        <!-- 帮助提示 -->
        <div v-if="currentStep < 5" class="mt-8 bg-gray-50 rounded-lg p-4">
          <h3 class="font-medium mb-2 flex items-center">
            <svg class="w-5 h-5 text-primary mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            需要帮助？
          </h3>
          <p class="text-sm text-gray-600">
            如果您在认购过程中遇到任何问题，请联系我们的客服团队：
            <span class="text-primary">400-123-4567</span>（工作日 9:00-18:00）
          </p>
        </div>
      </div>
    </div>
  </div>
</template> 