<script setup>
import { ref, reactive, onMounted } from 'vue'
import { getPublicDramas } from '../api/dramaService'

// 投资方案探索器状态
const currentStep = ref(1)
const showConsultationModal = ref(false)

// 用户偏好数据
const userPreferences = reactive({
  investmentAmount: '',
  genrePreference: [],
  riskProfile: '',
  investmentPeriod: ''
})

// 推荐的短剧组合
const recommendedDramas = ref([])
// 所有可用的短剧数据
const allDramas = ref([])
// 加载状态
const loading = ref(false)

// 投资金额选项
const investmentAmountOptions = [
  { value: '500k', label: '50万元', desc: '适合初次接触短剧投资的投资者' },
  { value: '1m', label: '100万元', desc: '平衡风险与机会的理想选择' },
  { value: '2m', label: '200万元', desc: '获得更多优质项目投资机会' },
  { value: '5m', label: '500万元', desc: '享受专业投资组合管理服务' },
  { value: '10m', label: '1000万元以上', desc: '定制化投资方案，专属服务' }
]

// 题材偏好选项
const genreOptions = [
  { value: 'urban', label: '都市情感', icon: '🏙️' },
  { value: 'costume', label: '古装玄幻', icon: '👘' },
  { value: 'suspense', label: '悬疑推理', icon: '🔍' },
  { value: 'comedy', label: '喜剧搞笑', icon: '😄' },
  { value: 'reality', label: '现实题材', icon: '🎭' },
  { value: 'youth', label: '青春校园', icon: '🎓' },
  { value: 'scifi', label: '科幻未来', icon: '🚀' },
  { value: 'family', label: '家庭温情', icon: '👨‍👩‍👧‍👦' }
]

// 风险偏好选项
const riskProfileOptions = [
  { value: 'conservative', label: '稳健型', desc: '优先选择制作完成度较高的项目' },
  { value: 'balanced', label: '平衡型', desc: '在风险和机会之间寻求平衡' },
  { value: 'aggressive', label: '进取型', desc: '愿意承担更高风险以获得更大机会' }
]

// 投资期限选项
const investmentPeriodOptions = [
  { value: '6m', label: '6个月', desc: '短期投资，快速回笼' },
  { value: '12m', label: '12个月', desc: '中期投资，稳健发展' },
  { value: '18m', label: '18个月', desc: '中长期投资，充分发展' },
  { value: '24m', label: '24个月', desc: '长期投资，价值最大化' }
]

// 格式化数字为金额
const formatCurrency = (value) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value)
}

// 加载短剧数据
const loadDramaData = async () => {
  loading.value = true
  try {
    const response = await getPublicDramas({
      page: 1,
      pageSize: 50,
      status: 'funding' // 只获取募资中的短剧
    })

    if (response.data && response.data.success) {
      allDramas.value = response.data.data.list
    }
  } catch (error) {
    console.error('加载短剧数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 根据标签判断题材类型
const getGenreFromTags = (tags) => {
  if (!Array.isArray(tags)) return 'other'

  const tagStr = tags.join(',').toLowerCase()

  if (tagStr.includes('都市') || tagStr.includes('现代') || tagStr.includes('职场')) return 'urban'
  if (tagStr.includes('古装') || tagStr.includes('宫廷') || tagStr.includes('武侠')) return 'costume'
  if (tagStr.includes('悬疑') || tagStr.includes('推理') || tagStr.includes('犯罪')) return 'suspense'
  if (tagStr.includes('喜剧') || tagStr.includes('搞笑') || tagStr.includes('轻松')) return 'comedy'
  if (tagStr.includes('现实') || tagStr.includes('社会') || tagStr.includes('励志')) return 'reality'
  if (tagStr.includes('青春') || tagStr.includes('校园') || tagStr.includes('学生')) return 'youth'
  if (tagStr.includes('科幻') || tagStr.includes('未来') || tagStr.includes('穿越')) return 'scifi'
  if (tagStr.includes('家庭') || tagStr.includes('亲情') || tagStr.includes('温情')) return 'family'

  return 'other'
}

// 根据募资进度判断制作阶段
const getStageFromProgress = (fundingProgress, remainingDays) => {
  if (fundingProgress >= 80) return 'filming' // 募资接近完成，可能已开拍
  if (fundingProgress >= 50) return 'prep' // 募资过半，筹备中
  if (fundingProgress >= 20) return 'script' // 有一定募资，剧本阶段
  return 'concept' // 募资刚开始，概念阶段
}

// 智能推荐算法
const generateRecommendations = () => {
  if (allDramas.value.length === 0) return []

  // 根据用户偏好筛选和排序短剧
  let filteredDramas = allDramas.value.map(drama => ({
    ...drama,
    genre: getGenreFromTags(drama.tags),
    stage: getStageFromProgress(drama.fundingProgress, drama.remainingDays),
    marketPotential: generateMarketPotential(drama),
    allocation: generateAllocation(drama)
  }))

  // 如果用户有题材偏好，优先推荐相关题材
  if (userPreferences.genrePreference.length > 0) {
    filteredDramas = filteredDramas.filter(drama =>
      userPreferences.genrePreference.includes(drama.genre) ||
      Math.random() > 0.7 // 保留30%的其他题材以保证多样性
    )
  }

  // 根据风险偏好调整
  if (userPreferences.riskProfile === 'conservative') {
    filteredDramas = filteredDramas.filter(drama =>
      drama.fundingProgress >= 30 // 稳健型偏好募资进度较高的项目
    )
  } else if (userPreferences.riskProfile === 'aggressive') {
    filteredDramas = filteredDramas.filter(drama =>
      drama.fundingProgress <= 70 // 进取型可以接受募资进度较低的项目
    )
  }

  // 随机选择10部，确保多样性
  const shuffled = filteredDramas.sort(() => 0.5 - Math.random())
  return shuffled.slice(0, 10)
}

// 生成市场潜力描述
const generateMarketPotential = (drama) => {
  const genre = getGenreFromTags(drama.tags)
  const potentials = {
    urban: '都市题材市场需求旺盛，具有广阔的商业前景',
    costume: '古装题材具有稳定的受众基础和较高的市场认知度',
    suspense: '悬疑推理类内容具有较强的用户粘性和讨论热度',
    comedy: '喜剧内容具有广泛的受众基础和较强的传播性',
    reality: '现实题材符合政策导向，具有良好的社会效益',
    youth: '青春题材具有较强的情感共鸣和市场活力',
    scifi: '科幻题材具有创新性和话题性，市场潜力巨大',
    family: '家庭题材具有稳定的收视群体和较好的口碑传播',
    other: '独特题材具有差异化优势，市场前景值得期待'
  }
  return potentials[genre] || potentials.other
}

// 生成投资占比
const generateAllocation = (drama) => {
  // 根据募资目标和当前进度生成合理的投资占比
  const baseAllocation = Math.floor(Math.random() * 10) + 5 // 5%-15%
  return `${baseAllocation}%`
}

// 开始投资方案探索
const startInvestmentPlan = () => {
  currentStep.value = 1
  // 重置用户偏好
  Object.assign(userPreferences, {
    investmentAmount: '',
    genrePreference: [],
    riskProfile: '',
    investmentPeriod: ''
  })
}

// 下一步
const nextStep = () => {
  if (currentStep.value < 3) {
    currentStep.value++
    if (currentStep.value === 2) {
      // 显示加载状态，然后自动进入第三步
      setTimeout(() => {
        recommendedDramas.value = generateRecommendations()
        currentStep.value = 3
      }, 2000) // 2秒后显示结果
    }
  }
}

// 上一步
const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--
  }
}

// 显示咨询弹窗
const showConsultation = () => {
  showConsultationModal.value = true
}

// 切换题材偏好
const toggleGenrePreference = (genre) => {
  const index = userPreferences.genrePreference.indexOf(genre)
  if (index === -1) {
    userPreferences.genrePreference.push(genre)
  } else {
    userPreferences.genrePreference.splice(index, 1)
  }
}

// 页面加载时获取短剧数据
onMounted(() => {
  loadDramaData()
})

// 投资方案特权（用于后面的投资特权展示）
const tierBenefits = {
  '钻石级': ['专属定制短剧IP', '优先分红权', '制作人署名权', '参与剧本研讨会', '全流程监督权'],
  '金牌': ['前3个项目优先选择权', '季度投资人会议', '项目专属顾问', '定期探班机会'],
  '银牌': ['项目进度实时通报', '半年度投资人会议', '作品首映礼VIP邀请'],
  '铜牌': ['季度财务报告', '年度投资人会议', '成片优先观看权']
}

// 常见问题
const faqs = ref([
  {
    question: '投资周期是多久？',
    answer: '标准投资周期为12个月，可选择续投。钻石级和金牌投资人可享受灵活退出机制。'
  },
  {
    question: '如何确保投资安全？',
    answer: '我们建立了完善的风控体系，包括资金专款专用、财务透明公示、定期审计，并提供合同收益保障条款。'
  },
  {
    question: '投资回报如何结算？',
    answer: '根据合同约定，基础收益按季度结算，额外收益在项目结束后30个工作日内一次性结算。'
  },
  {
    question: '能否参与多个项目？',
    answer: '可以，金牌及以上级别投资人可同时参与多个项目，并享受组合投资优惠。'
  }
])
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 投资方案头部 -->
    <section class="bg-gray-50 py-4">
      <div class="container mx-auto px-4">
        <div class="bg-gradient-primary text-white py-12 rounded-xl">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center px-8">
          <div>
            <h1 class="text-4xl md:text-5xl font-bold mb-6">灵活多样的投资方案</h1>
            <p class="text-xl opacity-90 mb-8">
              剧投投为您提供透明、高效、灵活的投资方式，让您的资金创造最大价值。
              根据投资金额，享受不同级别的权益与收益。
            </p>
            <div class="flex space-x-4">
              <button @click="startInvestmentPlan" class="btn bg-white text-primary hover:bg-blue-50">
                探索投资方案
              </button>
              <a href="#plans" class="btn border border-white text-white hover:bg-primary-dark">
                查看投资方案
              </a>
            </div>
          </div>
          
          <div class="hidden lg:flex justify-end">
            <!-- 使用SVG替代图片 -->
            <svg class="max-h-80 text-white" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="20" y="40" width="160" height="120" rx="10" stroke="currentColor" stroke-width="4"/>
              <path d="M40 80L80 120L120 80" stroke="currentColor" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M140 60L160 80L140 100" stroke="currentColor" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
              <circle cx="60" cy="60" r="10" stroke="currentColor" stroke-width="4"/>
              <path d="M20 140H180" stroke="currentColor" stroke-width="4"/>
              <path d="M60 140V160" stroke="currentColor" stroke-width="4"/>
              <path d="M100 140V160" stroke="currentColor" stroke-width="4"/>
              <path d="M140 140V160" stroke="currentColor" stroke-width="4"/>
            </svg>
          </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 投资方案探索器 -->
    <section id="calculator" class="py-16">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gradient">投资方案探索器</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            基于10部精选短剧的打包投资组合，为您提供专业的投资建议和风险分散方案
          </p>
        </div>

        <div class="max-w-6xl mx-auto">
          <!-- 步骤指示器 -->
          <div class="flex justify-center mb-8">
            <div class="flex items-center space-x-4">
              <div class="flex items-center">
                <div class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium"
                     :class="currentStep >= 1 ? 'bg-primary text-white' : 'bg-gray-200 text-gray-600'">
                  1
                </div>
                <span class="ml-2 text-sm font-medium" :class="currentStep >= 1 ? 'text-primary' : 'text-gray-600'">
                  投资偏好
                </span>
              </div>
              <div class="w-8 h-0.5" :class="currentStep >= 2 ? 'bg-primary' : 'bg-gray-200'"></div>
              <div class="flex items-center">
                <div class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium"
                     :class="currentStep >= 2 ? 'bg-primary text-white' : 'bg-gray-200 text-gray-600'">
                  2
                </div>
                <span class="ml-2 text-sm font-medium" :class="currentStep >= 2 ? 'text-primary' : 'text-gray-600'">
                  智能推荐
                </span>
              </div>
              <div class="w-8 h-0.5" :class="currentStep >= 3 ? 'bg-primary' : 'bg-gray-200'"></div>
              <div class="flex items-center">
                <div class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium"
                     :class="currentStep >= 3 ? 'bg-primary text-white' : 'bg-gray-200 text-gray-600'">
                  3
                </div>
                <span class="ml-2 text-sm font-medium" :class="currentStep >= 3 ? 'text-primary' : 'text-gray-600'">
                  投资方案
                </span>
              </div>
            </div>
          </div>

          <div class="card p-8 shadow-lg">
            <!-- 第一步：投资偏好收集 -->
            <div v-if="currentStep === 1" class="space-y-8">
              <div class="text-center mb-8">
                <h3 class="text-2xl font-bold mb-2">告诉我们您的投资偏好</h3>
                <p class="text-gray-600">我们将根据您的偏好为您推荐最适合的短剧投资组合</p>
              </div>

              <!-- 投资金额选择 -->
              <div>
                <h4 class="text-lg font-medium mb-4">投资金额档位</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
                  <div v-for="option in investmentAmountOptions" :key="option.value"
                       @click="userPreferences.investmentAmount = option.value"
                       class="p-4 border-2 rounded-lg cursor-pointer transition-all hover:border-primary"
                       :class="userPreferences.investmentAmount === option.value ? 'border-primary bg-blue-50' : 'border-gray-200'">
                    <div class="text-center">
                      <div class="text-lg font-bold text-primary">{{ option.label }}</div>
                      <div class="text-sm text-gray-600 mt-1">{{ option.desc }}</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 题材偏好选择 -->
              <div>
                <h4 class="text-lg font-medium mb-4">题材偏好（可多选）</h4>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div v-for="genre in genreOptions" :key="genre.value"
                       @click="toggleGenrePreference(genre.value)"
                       class="p-4 border-2 rounded-lg cursor-pointer transition-all hover:border-primary"
                       :class="userPreferences.genrePreference.includes(genre.value) ? 'border-primary bg-blue-50' : 'border-gray-200'">
                    <div class="text-center">
                      <div class="text-2xl mb-2">{{ genre.icon }}</div>
                      <div class="text-sm font-medium">{{ genre.label }}</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 风险偏好选择 -->
              <div>
                <h4 class="text-lg font-medium mb-4">风险偏好</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div v-for="risk in riskProfileOptions" :key="risk.value"
                       @click="userPreferences.riskProfile = risk.value"
                       class="p-4 border-2 rounded-lg cursor-pointer transition-all hover:border-primary"
                       :class="userPreferences.riskProfile === risk.value ? 'border-primary bg-blue-50' : 'border-gray-200'">
                    <div class="text-center">
                      <div class="font-bold text-primary">{{ risk.label }}</div>
                      <div class="text-sm text-gray-600 mt-1">{{ risk.desc }}</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 投资期限选择 -->
              <div>
                <h4 class="text-lg font-medium mb-4">投资期限</h4>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div v-for="period in investmentPeriodOptions" :key="period.value"
                       @click="userPreferences.investmentPeriod = period.value"
                       class="p-4 border-2 rounded-lg cursor-pointer transition-all hover:border-primary"
                       :class="userPreferences.investmentPeriod === period.value ? 'border-primary bg-blue-50' : 'border-gray-200'">
                    <div class="text-center">
                      <div class="font-bold text-primary">{{ period.label }}</div>
                      <div class="text-sm text-gray-600 mt-1">{{ period.desc }}</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 下一步按钮 -->
              <div class="text-center pt-6">
                <button @click="nextStep"
                        :disabled="!userPreferences.investmentAmount || !userPreferences.riskProfile || !userPreferences.investmentPeriod"
                        class="btn btn-primary px-8 py-3 text-lg disabled:opacity-50 disabled:cursor-not-allowed">
                  生成投资方案
                </button>
              </div>
            </div>

            <!-- 第二步：加载中 -->
            <div v-if="currentStep === 2" class="text-center py-16">
              <div class="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <h3 class="text-xl font-bold mb-2">正在为您生成专属投资方案...</h3>
              <p class="text-gray-600">基于您的偏好，我们正在从数据库中筛选最适合的短剧组合</p>
            </div>

            <!-- 第三步：投资方案展示 -->
            <div v-if="currentStep === 3" class="space-y-8">
              <div class="text-center mb-8">
                <h3 class="text-2xl font-bold mb-2">为您推荐的10部短剧投资组合</h3>
                <p class="text-gray-600">基于您的偏好，我们为您精选了以下短剧投资组合，实现风险分散和收益优化</p>
              </div>

              <!-- 投资组合概览 -->
              <div class="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg mb-8">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                  <div>
                    <div class="text-2xl font-bold text-primary">10部</div>
                    <div class="text-gray-600">精选短剧</div>
                  </div>
                  <div>
                    <div class="text-2xl font-bold text-primary">多元化</div>
                    <div class="text-gray-600">题材分布</div>
                  </div>
                  <div>
                    <div class="text-2xl font-bold text-primary">专业管理</div>
                    <div class="text-gray-600">风险控制</div>
                  </div>
                </div>
              </div>

              <!-- 短剧列表 -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div v-for="drama in recommendedDramas" :key="drama.id"
                     class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div class="flex items-start space-x-4">
                    <img :src="drama.cover || '/placeholder-drama.jpg'"
                         :alt="drama.title"
                         class="w-16 h-20 object-cover rounded">
                    <div class="flex-1">
                      <h4 class="font-bold text-lg mb-1">{{ drama.title }}</h4>
                      <div class="text-sm text-gray-600 mb-2">
                        <span class="inline-block bg-gray-100 px-2 py-1 rounded mr-2">{{ drama.director || '知名导演' }}</span>
                        <span class="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded">{{ drama.allocation }}</span>
                      </div>
                      <p class="text-sm text-gray-700 mb-2">{{ drama.description || '精彩剧情，值得期待' }}</p>
                      <div class="text-xs text-green-600">{{ drama.marketPotential }}</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 投资优势说明 -->
              <div class="bg-gray-50 p-6 rounded-lg">
                <h4 class="text-lg font-bold mb-4">投资组合优势</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h5 class="font-medium text-primary mb-2">🎯 风险分散</h5>
                    <p class="text-sm text-gray-600">10部不同题材、不同制作阶段的短剧，有效分散单一项目风险</p>
                  </div>
                  <div>
                    <h5 class="font-medium text-primary mb-2">📈 市场机会</h5>
                    <p class="text-sm text-gray-600">覆盖多个受众群体，把握不同市场机会和发展趋势</p>
                  </div>
                  <div>
                    <h5 class="font-medium text-primary mb-2">🏆 专业管理</h5>
                    <p class="text-sm text-gray-600">专业投资团队精选项目，全程跟踪管理，确保投资安全</p>
                  </div>
                  <div>
                    <h5 class="font-medium text-primary mb-2">💡 行业前景</h5>
                    <p class="text-sm text-gray-600">短剧行业快速发展，政策支持，市场前景广阔</p>
                  </div>
                </div>
              </div>

              <!-- 咨询按钮 -->
              <div class="text-center pt-6">
                <button @click="showConsultation" class="btn btn-primary px-8 py-3 text-lg mr-4">
                  咨询投资顾问
                </button>
                <button @click="prevStep" class="btn border border-gray-300 text-gray-700 px-6 py-3">
                  重新选择偏好
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 投资方案详情 -->
    <section id="plans" class="py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gradient">投资特权与权益</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            不同级别的投资人享受差异化的服务与权益，投资越多，回报越丰厚
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <!-- 铜牌 -->
          <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all overflow-hidden">
            <div class="bg-amber-100 px-6 py-4 border-b border-amber-200">
              <h3 class="text-2xl font-bold text-amber-800">铜牌</h3>
              <p class="text-amber-700">10万起投</p>
            </div>
            
            <div class="p-6">
              <h4 class="text-xl font-semibold mb-4">投资特权</h4>
              <ul class="space-y-3">
                <li v-for="(benefit, index) in tierBenefits['铜牌']" :key="index" class="flex items-start">
                  <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                  <span>{{ benefit }}</span>
                </li>
              </ul>
              
              <div class="mt-6">
                <p class="text-gray-700">投资特色</p>
                <p class="text-lg font-bold text-amber-600 mt-1">稳健增长</p>
              </div>
              
              <button class="w-full btn btn-primary mt-6">选择此方案</button>
            </div>
          </div>
          
          <!-- 银牌 -->
          <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all overflow-hidden">
            <div class="bg-gray-100 px-6 py-4 border-b border-gray-200">
              <h3 class="text-2xl font-bold text-gray-700">银牌</h3>
              <p class="text-gray-600">50万起投</p>
            </div>
            
            <div class="p-6">
              <h4 class="text-xl font-semibold mb-4">投资特权</h4>
              <ul class="space-y-3">
                <li v-for="(benefit, index) in tierBenefits['银牌']" :key="index" class="flex items-start">
                  <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                  <span>{{ benefit }}</span>
                </li>
              </ul>
              
              <div class="mt-6">
                <p class="text-gray-700">投资特色</p>
                <p class="text-lg font-bold text-gray-600 mt-1">均衡发展</p>
              </div>
              
              <button class="w-full btn btn-primary mt-6">选择此方案</button>
            </div>
          </div>
          
          <!-- 金牌 -->
          <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all ring-2 ring-yellow-500 relative">
            <div class="bg-yellow-100 rounded-t-xl px-6 py-4 border-b border-yellow-200 relative">
              <div class="absolute -top-3 -right-3 z-20">
                <div class="bg-yellow-500 text-white text-xs px-3 py-1 transform rotate-12 shadow-lg rounded">
                  推荐
                </div>
              </div>
              <h3 class="text-2xl font-bold text-yellow-800">金牌</h3>
              <p class="text-yellow-700">100万起投</p>
            </div>
            
            <div class="p-6">
              <h4 class="text-xl font-semibold mb-4">投资特权</h4>
              <ul class="space-y-3">
                <li v-for="(benefit, index) in tierBenefits['金牌']" :key="index" class="flex items-start">
                  <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                  <span>{{ benefit }}</span>
                </li>
              </ul>
              
              <div class="mt-6">
                <p class="text-gray-700">投资特色</p>
                <p class="text-lg font-bold text-yellow-600 mt-1">优质回报</p>
              </div>
              
              <button class="w-full btn btn-primary mt-6">选择此方案</button>
            </div>
          </div>
          
          <!-- 钻石级 -->
          <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all overflow-hidden">
            <div class="bg-blue-100 px-6 py-4 border-b border-blue-200">
              <h3 class="text-2xl font-bold text-blue-800">钻石级</h3>
              <p class="text-blue-700">300万起投</p>
            </div>
            
            <div class="p-6">
              <h4 class="text-xl font-semibold mb-4">投资特权</h4>
              <ul class="space-y-3">
                <li v-for="(benefit, index) in tierBenefits['钻石级']" :key="index" class="flex items-start">
                  <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                  <span>{{ benefit }}</span>
                </li>
              </ul>
              
              <div class="mt-6">
                <p class="text-gray-700">投资特色</p>
                <p class="text-lg font-bold text-blue-600 mt-1">卓越表现</p>
              </div>
              
              <button class="w-full btn btn-primary mt-6">选择此方案</button>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 常见问题 -->
    <section class="py-16">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gradient">常见问题</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            关于投资流程、收益结算、风险管控等问题的解答
          </p>
        </div>
        
        <div class="max-w-3xl mx-auto">
          <div v-for="(faq, index) in faqs" :key="index" class="mb-6">
            <div class="flex items-start">
              <div class="bg-blue-100 text-blue-600 rounded-full w-8 h-8 flex items-center justify-center mr-4 mt-1 flex-shrink-0">
                <span class="font-bold">Q</span>
              </div>
              <h3 class="text-xl font-semibold">{{ faq.question }}</h3>
            </div>
            <div class="ml-12 mt-2 text-gray-600">
              {{ faq.answer }}
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 投资咨询 -->
    <section class="bg-gradient-primary text-white py-12">
      <div class="container mx-auto px-4">
        <div class="bg-gradient-primary rounded-xl p-8 flex flex-col md:flex-row items-center justify-between">
          <div>
            <h2 class="text-2xl md:text-3xl font-bold mb-2">准备好开始您的投资了吗？</h2>
            <p class="opacity-90">我们的投资顾问随时为您提供专业咨询服务</p>
          </div>
          <div class="mt-6 md:mt-0">
            <button @click="showConsultation" class="btn bg-white text-primary hover:bg-blue-50 px-8 py-3 rounded-lg">
              立即咨询
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- 咨询弹窗 -->
    <div v-if="showConsultationModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click="showConsultationModal = false">
      <div class="bg-white rounded-xl p-8 max-w-md w-full mx-4" @click.stop>
        <div class="text-center">
          <div class="w-20 h-20 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-10 h-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
          <h3 class="text-xl font-bold mb-2">专业投资顾问</h3>
          <p class="text-gray-600 mb-4">我们的专业投资顾问将为您提供一对一的投资咨询服务</p>

          <div class="bg-gray-50 rounded-lg p-4 mb-6">
            <div class="text-sm text-gray-600 mb-2">扫码添加微信咨询</div>
            <div class="w-32 h-32 bg-gray-200 rounded-lg mx-auto flex items-center justify-center">
              <svg class="w-16 h-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z" />
              </svg>
            </div>
            <div class="text-xs text-gray-500 mt-2">投资顾问微信二维码</div>
          </div>

          <div class="space-y-2 text-sm text-gray-600 mb-6">
            <div>📞 咨询热线：400-888-8888</div>
            <div>📧 邮箱：<EMAIL></div>
            <div>⏰ 服务时间：9:00-18:00（工作日）</div>
          </div>

          <button @click="showConsultationModal = false" class="w-full py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-lg transition-colors">
            关闭
          </button>
        </div>
      </div>
    </div>
  </div>
</template>