<script setup>
import { ref, computed, onMounted } from 'vue';
import { getPublicDramas } from '../api/dramaService';
import { useTags } from '../composables/useTags';
import { useRouter } from 'vue-router';
import DramaTag from '../components/common/DramaTag.vue';

// 项目数据相关状态
const projects = ref([]);
const isLoading = ref(false);
const error = ref(null);
const router = useRouter();

// 标签管理
const { loadTags, tagMap, parseTagData } = useTags();

// 加载短剧数据
const loadProjects = async () => {
  isLoading.value = true;
  error.value = null;

  try {
    const response = await getPublicDramas({
      page: 1,
      pageSize: 50
      // 不指定status，获取所有短剧（包括剩余天数为0的）
    });

    if (response.data && response.data.success) {
      // 转换API数据格式
      projects.value = response.data.data.list.map(drama => ({
        id: drama.id,
        title: drama.title || '未命名短剧',
        cover: drama.cover || '',
        tags: Array.isArray(drama.tags) ? drama.tags : [],
        fundingGoal: Number(drama.fundingGoal) || 0,
        currentFunding: Number(drama.currentFunding) || 0,
        remainingDays: Number(drama.remainingDays) || 0,
        description: drama.description || '',
        episodeCount: Number(drama.episodes) || 0,
        duration: `${Number(drama.episodeLength) || 0}分钟/集`,
        fundingProgress: drama.fundingProgress || 0
      }));
    } else {
      error.value = '获取短剧数据失败';
    }
  } catch (err) {
    console.error('加载短剧数据失败:', err);
    error.value = '加载短剧数据失败';
  } finally {
    isLoading.value = false;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadTags(); // 加载标签数据
  loadProjects();
});

// 搜索和筛选
const searchQuery = ref('');
const selectedTags = ref([]);
const selectedTagsString = ref(''); // 用于下拉框的单选标签
const sortOption = ref('default'); // default, funding, time

// 所有可选标签（使用真实的API数据）
const allTags = computed(() => {
  // 从useTags获取真实的标签数据
  return tagMap.value.size > 0
    ? Array.from(tagMap.value.values()).map(tag => tag.name)
    : [];
});

// 筛选后的项目
const filteredProjects = computed(() => {
  return projects.value.filter(project => {
    // 搜索条件
    const matchesSearch = searchQuery.value === '' || 
      project.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      project.description.toLowerCase().includes(searchQuery.value.toLowerCase());
    
    // 标签筛选（OR逻辑：只要包含任意一个选中标签就显示）
    const matchesTags = selectedTags.value.length === 0 ||
      selectedTags.value.some(tag => project.tags.includes(tag));
    
    return matchesSearch && matchesTags;
  });
});

// 排序后的项目
const sortedProjects = computed(() => {
  const sorted = [...filteredProjects.value];
  
  if (sortOption.value === 'funding') {
    // 按募资进度从高到低
    sorted.sort((a, b) => {
      const progressA = (a.currentFunding / a.fundingGoal) * 100;
      const progressB = (b.currentFunding / b.fundingGoal) * 100;
      return progressB - progressA;
    });
  } else if (sortOption.value === 'time') {
    // 按剩余时间从高到低（剩余天数大的排在前面）
    sorted.sort((a, b) => b.remainingDays - a.remainingDays);
  }
  
  return sorted;
});

// 处理标签下拉框变化
const handleTagChange = () => {
  if (selectedTagsString.value) {
    selectedTags.value = [selectedTagsString.value];
  } else {
    selectedTags.value = [];
  }
};

// 清除筛选
const clearFilters = () => {
  searchQuery.value = '';
  selectedTags.value = [];
  selectedTagsString.value = '';
  sortOption.value = 'default';
};

// 重新加载数据
const reloadData = () => {
  loadProjects();
};

// 计算进度条百分比
const calculateProgress = (current, goal) => {
  return Math.min(Math.round((current / goal) * 100), 100);
};

// 格式化金额
const formatCurrency = (amount) => {
  if (amount >= 10000000) {
    return (amount / 10000000).toFixed(2) + '千万';
  } else if (amount >= 10000) {
    return (amount / 10000).toFixed(2) + '万';
  } else {
    return amount.toString();
  }
};

// 导航到项目详情页
const navigateToProjectDetail = (projectId, event) => {
  // 如果点击事件来自按钮组内部元素，则不触发导航
  if (event.target.closest('.bg-white.p-2.border-t')) {
    return;
  }

  router.push(`/project/${projectId}`);
};
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 页面标题 -->
    <section class="bg-gray-50 py-4">
      <div class="container mx-auto px-4">
        <div class="bg-gradient-primary text-white py-6 text-center">
          <h1 class="text-4xl md:text-5xl font-bold mb-2">短剧筹募</h1>
          <p class="text-xl max-w-3xl mx-auto opacity-90">
            精选优质短剧项目，投资未来爆款内容，共享短剧市场红利
          </p>
        </div>
      </div>
    </section>

      <div class="container mx-auto px-4">
        <!-- 加载状态 -->
        <div v-if="isLoading" class="flex justify-center items-center py-8">
          <div class="w-16 h-16 border-4 border-gray-200 border-t-primary rounded-full animate-spin"></div>
        </div>

        <!-- 错误提示 -->
        <div v-else-if="error" class="text-center py-8">
          <svg class="w-16 h-16 mx-auto text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          <p class="mt-4 text-lg text-red-600">{{ error }}</p>
          <button
            @click="reloadData"
            class="mt-4 px-6 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
          >
            重试
          </button>
        </div>

        <template v-else>
        <!-- 筛选栏 -->
        <section class="py-4">
          <div class="bg-white rounded-xl shadow-md p-4 mb-3">
            <div class="flex flex-col lg:flex-row gap-4 items-center">
              <!-- 搜索框 -->
              <div class="relative flex-1 w-full lg:w-auto">
                <input
                  v-model="searchQuery"
                  type="text"
                  placeholder="搜索短剧项目"
                  class="w-full pl-10 pr-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
                />
                <svg class="absolute left-3 top-3 w-5 h-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>

              <!-- 类型筛选下拉框 -->
              <div class="w-full lg:w-48">
                <select
                  v-model="selectedTagsString"
                  @change="handleTagChange"
                  class="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
                >
                  <option value="">全部类型</option>
                  <option v-for="tag in allTags" :key="tag" :value="tag">
                    {{ tag }}
                  </option>
                </select>
              </div>

              <!-- 排序选择下拉框 -->
              <div class="w-full lg:w-40">
                <select
                  v-model="sortOption"
                  class="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
                >
                  <option value="default">默认排序</option>
                  <option value="funding">募资进度</option>
                  <option value="time">剩余时间</option>
                </select>
              </div>

              <!-- 重置按钮 -->
              <button
                @click="clearFilters"
                class="w-full lg:w-auto px-4 py-2.5 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center gap-2"
              >
                <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                重置
              </button>
            </div>
          </div>
        </section>

        <!-- 项目列表 -->
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-x-4 gap-y-8">
          <div
            v-for="project in sortedProjects"
            :key="project.id"
            class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1 relative group border border-gray-100 hover:border-gray-200 cursor-pointer flex flex-col h-full"
            @click="navigateToProjectDetail(project.id, $event)"
          >
            <!-- 项目封面 -->
            <div class="relative">
              <!-- 封面图区域 (3:4比例) -->
              <div class="relative pt-[133.33%] bg-gray-100 overflow-hidden">
                <!-- 封面图 -->
                <div class="absolute inset-0 flex items-center justify-center transition-transform duration-500 group-hover:scale-105">
                  <img 
                    v-if="project.cover"
                    :src="project.cover" 
                    :alt="project.title" 
                    class="w-full h-full object-cover"
                  >
                  <div v-else class="w-full h-full flex items-center justify-center bg-purple-100">
                    <svg class="w-20 h-20 md:w-24 md:h-24 lg:w-20 lg:h-20 text-primary" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12,17.27L18.18,21l-1.64-7.03L22,9.24l-7.19-0.61L12,2L9.19,8.63L2,9.24l5.46,4.73L5.82,21L12,17.27z" />
                    </svg>
                  </div>
                </div>
                
                <!-- 剩余天数角标 -->
                <div class="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 text-xs md:text-sm lg:text-xs font-bold rounded-md shadow-md">
                  剩余 {{ project.remainingDays }} 天
                </div>
              </div>
            </div>
            
            <!-- 内容区域 -->
            <div class="p-3 md:p-4 lg:p-3 flex-1 flex flex-col">
              <!-- 标题 -->
              <h3 class="text-sm md:text-base lg:text-sm font-bold mb-1 h-6 md:h-7 lg:h-6 hover:text-primary transition-colors overflow-hidden">
                <div class="whitespace-nowrap animate-scroll-if-overflow">
                  {{ project.title }}
                </div>
              </h3>
              
              <!-- 类型标签 -->
              <div class="flex gap-1 mb-1 overflow-hidden">
                <DramaTag
                  v-for="tag in parseTagData(project.tags)"
                  :key="tag.id || tag.name"
                  :tag="tag"
                  class="text-[10px] md:text-xs lg:text-[10px] flex-shrink-0"
                />
              </div>
              
              <!-- 募资进度条 -->
              <div>
                <div class="flex justify-between text-xs md:text-sm lg:text-xs mb-1">
                  <span class="text-gray-600">已筹 {{ formatCurrency(project.currentFunding) }}</span>
                  <span class="font-medium">{{ calculateProgress(project.currentFunding, project.fundingGoal) }}%</span>
                </div>
                <div class="h-2 md:h-2.5 lg:h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div 
                    class="h-full rounded-full bg-primary" 
                    :style="{
                      width: `${calculateProgress(project.currentFunding, project.fundingGoal)}%`
                    }"
                  ></div>
                </div>
                <div class="text-[10px] md:text-xs lg:text-[10px] text-gray-500 mt-1">
                  目标 {{ formatCurrency(project.fundingGoal) }}
                </div>
              </div>
            </div>
            
            <!-- 常驻按钮组 -->
            <div class="bg-white pt-2 px-3 pb-3 flex gap-2 mt-auto">
              <RouterLink
                :to="`/project/${project.id}`"
                class="flex-1 py-2 px-3 rounded-lg flex items-center justify-center bg-gray-100 hover:bg-gray-200 text-gray-800 text-center font-medium text-xs transition-colors"
                @click.stop
              >
                <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542 7z" />
                </svg>
                查看详情
              </RouterLink>
              <button
                @click="handleInvestment(project)"
                class="flex-1 py-2 px-3 rounded-lg flex items-center justify-center bg-primary hover:bg-primary-dark text-white text-center font-medium text-xs transition-colors"
              >
                <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v2a2 2 0 002 2z" />
                </svg>
                参与众筹
              </button>
            </div>
          </div>
        </div>
        
        <!-- 无结果提示 -->
        <div v-if="projects.length > 0 && sortedProjects.length === 0" class="text-center py-6">
          <svg class="w-16 h-16 mx-auto text-gray-300 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 class="text-xl font-bold text-gray-700 mb-1">未找到匹配项目</h3>
          <p class="text-gray-500">请尝试调整搜索条件或清除筛选器</p>
          <button 
            @click="clearFilters" 
            class="mt-4 px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
          >
            清除筛选
          </button>
        </div>
        
        <!-- 无数据提示 -->
        <div v-if="projects.length === 0 && !isLoading && !error" class="text-center py-6">
          <svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
          </svg>
          <h3 class="text-xl font-bold text-gray-700 mb-1">暂无募资中的短剧项目</h3>
          <p class="text-gray-500">请稍后再来查看</p>
        </div>
      </template>
      </div>
  </div>
</template>

<style scoped>
/* 引入统一渐变色文本样式 */
.text-gradient {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary;
}

/* 标题滚动动画 */
.animate-scroll-if-overflow {
  display: inline-block;
  animation: scroll-text 8s linear infinite;
  animation-play-state: paused;
}

/* 当文本溢出时启用滚动动画 */
h3:hover .animate-scroll-if-overflow {
  animation-play-state: running;
}

@keyframes scroll-text {
  0% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(0);
  }
  75% {
    transform: translateX(calc(-100% + 100px));
  }
  100% {
    transform: translateX(calc(-100% + 100px));
  }
}
</style> 